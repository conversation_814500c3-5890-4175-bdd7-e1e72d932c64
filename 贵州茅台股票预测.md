# 贵州茅台股票预测项目

本项目基于FEDformer时间序列预测模型，对贵州茅台股票进行收盘价预测分析。

## 项目概述

本项目使用FEDformer（Frequency Enhanced Decomposed Transformer）模型对贵州茅台股票的收盘价进行预测。FEDformer是一种基于Transformer架构的时间序列预测模型，通过傅里叶变换增强特征提取能力，特别适合处理金融时间序列数据。
python run_stock.py --model Informer --train_epochs 10 --patience 20
mdoel 模型  train_epochs 训练轮次  patience 早停轮次

## 数据特征

预测使用以下特征：

1. **基础价格数据**：
   - 开盘价（'开盘'）
   - 最高价（'最高'）
   - 最低价（'最低'）
   - 收盘价（'收盘'）

2. **交易量指标**：
   - 成交量（'成交量'）
   - 成交额（'成交额'）

3. **收益指标**：
   - 收益（'收益'）

4. **技术分析指标**：
   - 10日移动平均线（'ma_10'）
   - 20日移动平均线（'ma_20'）
   - 20日指数移动平均线（'ema_20'）
   - 布林带上轨（'boll_ub'）
   - 布林带下轨（'boll_lb'）
   - 5日乖离率（'BIAS1'）
   - 10日乖离率（'BIAS2'）
   - 20日乖离率（'BIAS3'）
   - 动量指标（'MTM'）

## 预测目标

- 目标变量：下一个交易日的收盘价
- 预测方法：使用过去60天的数据预测未来30天的收盘价

## 数据处理流程

1. **数据加载**：从Excel文件中读取贵州茅台股票数据
2. **特征工程**：
   - 计算技术指标（如缺失）
   - 添加目标变量（下一日收盘价）
3. **数据分割**：按8:2的比例分割为训练集和测试集
4. **数据标准化**：使用StandardScaler对特征进行标准化
5. **时间特征编码**：对日期信息进行编码

## 模型架构

FEDformer模型的主要组件：

1. **嵌入层**：将输入数据转换为适合模型处理的形式
2. **傅里叶增强块**：使用傅里叶变换增强时间序列特征提取
3. **编码器-解码器结构**：类似于标准Transformer的结构
4. **多头自注意力机制**：捕捉数据中的时间依赖关系

## 模型配置

- 序列长度：60
- 标签长度：30
- 预测长度：30
- 模型维度：512
- 注意力头数：8
- 编码器层数：2
- 解码器层数：1
- 前馈网络维度：2048

## 评估指标

模型使用以下指标评估预测性能：

- **MSE**（均方误差）：预测值与真实值差异的平方平均
- **RMSE**（均方根误差）：MSE的平方根，表示预测误差的标准差
- **MAE**（平均绝对误差）：预测值与真实值绝对差异的平均
- **R²**（决定系数）：模型解释的目标变量方差比例

## 使用说明

### 环境配置

1. 创建conda环境：
   ```
   conda create -n fedformer python=3.8 -y
   conda activate fedformer
   ```

2. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

### 运行预测

执行以下命令启动预测：
```
python run_stock.py
```

可以修改`run_stock.py`中的参数来调整模型配置，如：
- `--train_epochs`：训练轮数
- `--seq_len`：输入序列长度
- `--pred_len`：预测序列长度

### 结果输出

训练和预测完成后，结果将保存在`./results/`目录下：
- 预测数据：`pred.npy`
- 真实数据：`true.npy`
- 评估指标：`metrics.npy`
- 预测可视化图表：`prediction.png`

## 项目结构

```
├── data_provider/         # 数据加载和处理模块
│   ├── data_factory.py    # 数据提供工厂
│   └── data_loader.py     # 数据加载器
├── exp/                   # 实验相关模块
│   ├── exp_basic.py       # 基础实验类
│   └── exp_main.py        # 主实验类
├── layers/                # 模型层定义
├── models/                # 模型定义
├── utils/                 # 工具函数
├── run_stock.py           # 股票预测主脚本
├── requirements.txt       # 项目依赖
└── 贵州茅台.xlsx            # 股票数据文件
```

## 模型优化建议

1. **特征选择**：可以尝试添加更多技术指标或基本面指标
2. **超参数调优**：可以使用网格搜索或贝叶斯优化来寻找最佳超参数
3. **模型集成**：可以尝试将FEDformer与其他模型（如LSTM、GRU）结果进行集成
4. **数据增强**：增加历史数据范围或使用数据增强技术

## 参考资料

- FEDformer原始论文：[FEDformer: Frequency Enhanced Decomposed Transformer for Long-term Series Forecasting](https://arxiv.org/abs/2201.12740)
- 原始项目地址：[https://github.com/MAZiqing/FEDformer](https://github.com/MAZiqing/FEDformer) 
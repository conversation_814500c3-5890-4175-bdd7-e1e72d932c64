import argparse
import os
import torch
from exp.exp_main import Exp_Main
import random
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score

def main():
    fix_seed = 2021
    random.seed(fix_seed)
    torch.manual_seed(fix_seed)
    np.random.seed(fix_seed)

    parser = argparse.ArgumentParser(description='贵州茅台股票预测 - 改进版')

    # 基本配置
    parser.add_argument('--is_training', type=int, default=1, help='训练状态')
    parser.add_argument('--task_id', type=str, default='stock_predict', help='任务ID')
    parser.add_argument('--model', type=str, default='Autoformer',
                        help='模型名称, 选项: [FEDformer, Autoformer, Informer, Transformer]')

    # FEDformer模型的补充配置
    parser.add_argument('--version', type=str, default='Fourier',
                        help='对于FEDformer, 有两个版本可选, 选项: [Fourier, Wavelets]')
    parser.add_argument('--mode_select', type=str, default='random',
                        help='对于FEDformer, 有两种模式选择方法, 选项: [random, low]')
    parser.add_argument('--modes', type=int, default=64, help='随机选择的模式数 64')
    parser.add_argument('--L', type=int, default=3, help='忽略级别')
    parser.add_argument('--base', type=str, default='legendre', help='小波变换基函数')
    parser.add_argument('--cross_activation', type=str, default='tanh',
                        help='小波变换交叉注意力激活函数 tanh 或 softmax')

    # 数据加载
    parser.add_argument('--data', type=str, default='stock', help='数据集类型')
    parser.add_argument('--root_path', type=str, default='./', help='数据文件的根路径')
    parser.add_argument('--data_path', type=str, default='贵州茅台.xlsx', help='数据文件')
    parser.add_argument('--features', type=str, default='MS',
                        help='预测任务, 选项:[M, S, MS]; M:多变量预测多变量, S:单变量预测单变量, MS:多变量预测单变量')
    parser.add_argument('--target', type=str, default='收盘', help='在S或MS任务中的目标特征')
    parser.add_argument('--freq', type=str, default='d',
                        help='时间特征编码的频率, 选项:[s:秒, t:分钟, h:小时, d:天, b:工作日, w:周, m:月]')
    parser.add_argument('--detail_freq', type=str, default='d', help='类似freq, 但用于预测')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='模型检查点位置')

    # 预测任务
    parser.add_argument('--seq_len', type=int, default=60, help='输入序列长度')
    parser.add_argument('--label_len', type=int, default=30, help='起始标记长度')
    parser.add_argument('--pred_len', type=int, default=30, help='预测序列长度')

    # 模型定义
    parser.add_argument('--enc_in', type=int, default=16, help='编码器输入大小')
    parser.add_argument('--dec_in', type=int, default=16, help='解码器输入大小')
    parser.add_argument('--c_out', type=int, default=1, help='输出大小')
    parser.add_argument('--d_model', type=int, default=512, help='模型维度')
    parser.add_argument('--n_heads', type=int, default=8, help='头数')
    parser.add_argument('--e_layers', type=int, default=2, help='编码器层数')
    parser.add_argument('--d_layers', type=int, default=1, help='解码器层数')
    parser.add_argument('--d_ff', type=int, default=2048, help='全连接网络维度')
    parser.add_argument('--moving_avg', default=[24], help='移动平均窗口大小')
    parser.add_argument('--factor', type=int, default=1, help='注意力因子')
    parser.add_argument('--distil', action='store_false',
                        help='是否在编码器中使用蒸馏, 使用此参数表示不使用蒸馏',
                        default=True)
    parser.add_argument('--dropout', type=float, default=0.05, help='dropout率')
    parser.add_argument('--embed', type=str, default='timeF',
                        help='时间特征编码, 选项:[timeF, fixed, learned]')
    parser.add_argument('--activation', type=str, default='gelu', help='激活函数')
    parser.add_argument('--output_attention', action='store_true', help='是否在编码器中输出注意力')
    parser.add_argument('--do_predict', action='store_true', help='是否预测未见的未来数据')

    # 优化 - 改进的参数
    parser.add_argument('--num_workers', type=int, default=10, help='数据加载器的工作线程数')
    parser.add_argument('--itr', type=int, default=1, help='实验次数')
    parser.add_argument('--train_epochs', type=int, default=20, help='训练轮数')  # 增加训练轮数
    parser.add_argument('--batch_size', type=int, default=32, help='训练输入数据的批次大小')
    parser.add_argument('--patience', type=int, default=7, help='早停耐心值')  # 增加耐心值
    parser.add_argument('--learning_rate', type=float, default=0.0001, help='优化器学习率')
    parser.add_argument('--des', type=str, default='improved', help='实验描述')
    parser.add_argument('--loss', type=str, default='mse', help='损失函数')
    parser.add_argument('--lradj', type=str, default='type1', help='调整学习率')
    parser.add_argument('--use_amp', action='store_true', help='使用自动混合精度训练', default=False)

    # GPU
    parser.add_argument('--use_gpu', type=bool, default=True, help='使用gpu')
    parser.add_argument('--gpu', type=int, default=0, help='gpu')
    parser.add_argument('--use_multi_gpu', action='store_true', help='使用多个gpu', default=False)
    parser.add_argument('--devices', type=str, default='0,1', help='多gpu的设备id')

    args = parser.parse_args()

    # 根据所选模型调整参数
    if args.model in ['Autoformer', 'Informer', 'Transformer']:
        # 这些模型需要特殊处理
        if args.features == 'MS':
            # 如果是多变量预测单变量，需要修改为多变量预测多变量
            args.features = 'M'
            args.c_out = args.enc_in
            print(f"注意: 为了兼容{args.model}模型，已将features从'MS'改为'M'，并将c_out设置为{args.c_out}")
    
    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False

    if args.use_gpu and args.use_multi_gpu:
        args.devices = args.devices.replace(' ', '')
        device_ids = args.devices.split(',')
        args.device_ids = [int(id_) for id_ in device_ids]
        args.gpu = args.device_ids[0]

    print('实验参数:')
    print(args)

    Exp = Exp_Main

    if args.is_training:
        for ii in range(args.itr):
            # 设置实验记录
            setting = '{}_{}_{}_modes{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                args.task_id,
                args.model,
                args.mode_select,
                args.modes,
                args.data,
                args.features,
                args.seq_len,
                args.label_len,
                args.pred_len,
                args.d_model,
                args.n_heads,
                args.e_layers,
                args.d_layers,
                args.d_ff,
                args.factor,
                args.embed,
                args.distil,
                args.des,
                ii)

            exp = Exp(args)  # 设置实验
            print('>>>>>>>开始训练 : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
            exp.train(setting)

            print('>>>>>>>测试 : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
            mae, mse, rmse, mape, mspe, r2 = exp.test(setting)
            
            print('测试指标:')
            print('MAE: {:.4f}, MSE: {:.4f}, RMSE: {:.4f}, R2: {:.4f}'.format(mae, mse, rmse, r2))
            
            # 分析R²结果
            if r2 < 0:
                print(f"警告: R²为负数 ({r2:.4f})，这表明模型预测效果比简单均值预测还差")
                print("可能的原因:")
                print("1. 训练不充分 - 尝试增加训练轮数")
                print("2. 模型复杂度过高 - 尝试减少模型参数")
                print("3. 数据预处理问题 - 检查数据质量和特征工程")
                print("4. 超参数不合适 - 尝试调整学习率、批次大小等")
            elif r2 < 0.5:
                print(f"注意: R²较低 ({r2:.4f})，模型预测效果有待改善")
            else:
                print(f"良好: R²为 {r2:.4f}，模型预测效果较好")

            if args.do_predict:
                print('>>>>>>>预测 : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                exp.predict(setting, True)

            torch.cuda.empty_cache()

if __name__ == "__main__":
    main()
